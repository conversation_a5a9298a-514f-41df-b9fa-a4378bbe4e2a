import type {
  TaskSearchParams,
  TaskApiResponse,
  TaskAlert,
  DBConnection,
  AlertSend,
  OtherInfo,
  TaskBasic,
  TaskBasicFormDataAdd,
  TaskBasicFormDataUpdateOrDelete,
} from '../types/task';
import {
  generateDBConnectionData,
  generateAlertSendData,
  generateOtherInfoData,
} from '../utils/generateMockData';

/**
 * 生成模拟任务数据
 */
function generateMockTasks(count: number): TaskBasic[] {
  const tasks: TaskBasic[] = [];
  const groups = ['系统维护', '数据备份', '监控告警', '日志清理', '性能优化'];
  const frequencyValues = [10, 20, 30, 40, 60];
  const frequencyUnits = ['sec', 'min', 'hour', 'day'];
  const retryFrequencyValues = [5, 10, 15, 30, 45, 60];
  const retryFrequencyUnits = ['sec', 'min', 'hour'];

  for (let i = 1; i <= count; i++) {
    const startHour = Math.floor(Math.random() * 24);
    const startMinute = Math.floor(Math.random() * 60);
    const endHour = (startHour + Math.floor(Math.random() * 3) + 1) % 24;
    const endMinute = Math.floor(Math.random() * 60);

    // 随机生成执行频率和重试频率
    const freqValue =
      frequencyValues[Math.floor(Math.random() * frequencyValues.length)];
    const freqUnit =
      frequencyUnits[Math.floor(Math.random() * frequencyUnits.length)];
    const retryFreqValue =
      retryFrequencyValues[
        Math.floor(Math.random() * retryFrequencyValues.length)
      ];
    const retryFreqUnit =
      retryFrequencyUnits[
        Math.floor(Math.random() * retryFrequencyUnits.length)
      ];

    tasks.push({
      id: i,
      name: `任务${i.toString().padStart(3, '0')}`,
      group: groups[Math.floor(Math.random() * groups.length)],
      status: Math.random() > 0.3 ? 'enabled' : 'disabled', // 70%的任务启用
      start_time: `${startHour.toString().padStart(2, '0')}:${startMinute.toString().padStart(2, '0')}:00`,
      update_time: new Date().toISOString().slice(0, 19).replace('T', ' '),
      end_time: `${endHour.toString().padStart(2, '0')}:${endMinute.toString().padStart(2, '0')}:00`,
      weekday:
        Math.random() > 0.5
          ? [Math.floor(Math.random() * 7 + 1).toString()]
          : ['1', '2', '3', '4', '5'],
      frequency: `${freqValue}${freqUnit}`,
      retry_num: Math.floor(Math.random() * 5).toString(),
      retry_frequency: `${retryFreqValue}${retryFreqUnit}`,
      alert_task_id: [`alert_${Math.floor(Math.random() * 10) + 1}`],
      alert_send_id: [`send_${Math.floor(Math.random() * 5) + 1}`],
      db_connection_id: `db_${Math.floor(Math.random() * 3) + 1}`,
      other_info_id: `info_${Math.floor(Math.random() * 5) + 1}`,
      create_time: new Date(
        Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000
      )
        .toISOString()
        .slice(0, 19)
        .replace('T', ' '),
    });
  }

  return tasks;
}

/**
 * 模拟数据 - 生成1000条任务数据
 */
export const mockTaskData: TaskBasic[] = generateMockTasks(1000);

/**
 * 生成模拟告警数据
 */
function generateMockAlerts(count: number): TaskAlert[] {
  const alerts: TaskAlert[] = [];
  const severities = ['low', 'medium', 'high', 'critical'];
  const types = ['isExist', 'isEqual', 'isGreater', 'isLess'];
  const tableNames = [
    'users',
    'orders',
    'products',
    'logs',
    'sessions',
    'payments',
    'inventory',
    'customers',
    'transactions',
    'reports',
  ];
  const systemNames = [
    '用户系统',
    '订单系统',
    '支付系统',
    '库存系统',
    '日志系统',
    '监控系统',
    '报表系统',
    '消息系统',
    '文件系统',
    '缓存系统',
  ];
  const checkTypes = [
    '连接检查',
    '状态检查',
    '记录数检查',
    '性能检查',
    '空间检查',
    '权限检查',
    '配置检查',
    '服务检查',
    '网络检查',
    '安全检查',
  ];

  for (let i = 1; i <= count; i++) {
    const severity = severities[Math.floor(Math.random() * severities.length)];
    const type = types[Math.floor(Math.random() * types.length)];
    const tableName = tableNames[Math.floor(Math.random() * tableNames.length)];
    const systemName =
      systemNames[Math.floor(Math.random() * systemNames.length)];
    const checkType = checkTypes[Math.floor(Math.random() * checkTypes.length)];

    // 根据类型生成不同的SQL语句
    let sql = '';
    let values: string[] = [];

    switch (type) {
      case 'isExist':
        sql = `SELECT 1 FROM ${tableName} WHERE id = 1`;
        break;
      case 'isEqual':
        sql = `SELECT COUNT(*) FROM ${tableName}`;
        values = [Math.floor(Math.random() * 1000 + 100).toString()];
        break;
      case 'isGreater':
        sql = `SELECT COUNT(*) FROM ${tableName} WHERE status = 'active'`;
        values = [Math.floor(Math.random() * 50 + 10).toString()];
        break;
      case 'isLess':
        sql = `SELECT AVG(response_time) FROM ${tableName}_logs`;
        values = [Math.floor(Math.random() * 500 + 100).toString()];
        break;
    }

    const createTime = new Date(
      Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000
    );
    const updateTime = new Date(
      createTime.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000
    );

    alerts.push({
      id: i,
      name: `${systemName}${checkType}_${i.toString().padStart(3, '0')}`,
      severity,
      sql,
      type,
      values,
      create_time: createTime.toISOString().slice(0, 19).replace('T', ' '),
      update_time: updateTime.toISOString().slice(0, 19).replace('T', ' '),
    });
  }

  return alerts;
}

/**
 * 模拟告警数据 - 生成1000条告警数据
 */
export const mockAlertData: TaskAlert[] = generateMockAlerts(1000);

/**
 * 模拟数据库连接数据 - 生成1000条数据库连接数据
 */
export const mockDbConnectionData: DBConnection[] =
  generateDBConnectionData(1000);

/**
 * 模拟告警发送数据 - 生成1000条告警发送数据
 */
export const mockAlertSendData: AlertSend[] = generateAlertSendData(1000);

/**
 * 模拟其他信息数据 - 生成1000条其他信息数据
 */
export const mockOtherInfoData: OtherInfo[] = generateOtherInfoData(1000);

/**
 * 任务API服务类
 */
export class TaskService {
  /**
   * 获取任务列表
   * @param params 搜索参数
   * @returns 任务列表和总数
   */
  static async getTasks(
    params: TaskSearchParams
  ): Promise<TaskApiResponse<TaskBasic>> {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 300));

    // 模拟分页和搜索逻辑
    let filteredData = [...mockTaskData];

    // 根据搜索条件过滤数据
    if (params.name) {
      filteredData = filteredData.filter(item =>
        item.name.includes(params.name!)
      );
    }

    if (params.group) {
      filteredData = filteredData.filter(item =>
        item.group.includes(params.group!)
      );
    }

    if (params.status) {
      filteredData = filteredData.filter(item => item.status === params.status);
    }

    if (params.weekday) {
      filteredData = filteredData.filter(item =>
        item.weekday.includes(params.weekday!)
      );
    }

    if (params.frequency) {
      filteredData = filteredData.filter(
        item => item.frequency === params.frequency
      );
    }

    // 分页处理
    const { current = 1, pageSize = 10 } = params;
    const startIndex = (current - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedData = filteredData.slice(startIndex, endIndex);

    return {
      data: paginatedData,
      total: filteredData.length,
      success: true,
    };
  }

  /**
   * 删除单个任务
   * @param id 任务ID
   * @returns 是否删除成功
   */
  static async deleteTask(id: number): Promise<boolean> {
    console.log('删除任务:', id);
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500));

    return true; // 模拟成功
  }

  /**
   * 批量删除任务
   * @param ids 任务ID数组
   * @returns 是否删除成功
   */
  static async batchDeleteTasks(ids: number[]): Promise<boolean> {
    console.log('批量删除任务:', ids);
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 800));

    return true; // 模拟成功
  }

  /**
   * 添加任务
   * @param taskData 任务数据
   * @returns 新创建的任务信息
   */
  static async addTask(
    taskBasicFormDataAdd: TaskBasicFormDataAdd
  ): Promise<TaskBasicFormDataAdd> {
    console.log('添加任务:', taskBasicFormDataAdd);
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 600));

    // 模拟返回新创建的任务
    // const newTask: TaskBasicFormDataAdd = {
    // id: Date.now(),
    // ...taskBasicFormDataAdd,
    // createTime: new Date().toLocaleString(),
    // };

    return taskBasicFormDataAdd;
  }

  /**
   * 更新任务
   * @param id 任务ID
   * @param taskData 更新的任务数据
   * @returns 更新后的任务信息
   */
  static async updateTask(
    id: number,
    taskData: Partial<TaskBasicFormDataAdd>
  ): Promise<TaskBasicFormDataAdd> {
    console.log('更新任务:', id, taskData);
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500));

    // 模拟返回更新后的任务
    const existingTask = mockTaskData.find(task => task.id === id);
    if (!existingTask) {
      throw new Error('任务不存在');
    }

    return {
      ...existingTask,
      ...taskData,
    };
  }

  /**
   * 获取告警列表
   */
  static async getAlerts(): Promise<TaskAlert[]> {
    await new Promise(resolve => setTimeout(resolve, 200));
    return mockAlertData;
  }

  /**
   * 根据ID数组获取告警列表
   */
  static async getAlertsByIds(ids: string[]): Promise<TaskAlert[]> {
    await new Promise(resolve => setTimeout(resolve, 200));
    // 模拟根据ID查询
    return mockAlertData.filter(alert => ids.includes(`alert_${alert.id}`));
  }

  /**
   * 获取所有数据库连接列表
   */
  static async getDbConnections(): Promise<DBConnection[]> {
    await new Promise(resolve => setTimeout(resolve, 200));
    return mockDbConnectionData;
  }

  /**
   * 根据ID获取单个数据库连接
   */
  static async getDbConnectionByIdNumber(
    id: number
  ): Promise<DBConnection | null> {
    await new Promise(resolve => setTimeout(resolve, 200));
    // 模拟根据ID查询
    const connection = mockDbConnectionData.find(conn => conn.id === id);
    return connection || null;
  }

  /**
   * 根据ID获取数据库连接
   */
  static async getDbConnectionById(id: string): Promise<DBConnection | null> {
    await new Promise(resolve => setTimeout(resolve, 200));
    // 模拟根据ID查询
    const connection = mockDbConnectionData.find(
      conn => `db_${conn.id}` === id
    );
    return connection || null;
  }

  /**
   * 获取告警发送列表
   */
  static async getAlertSends(): Promise<AlertSend[]> {
    await new Promise(resolve => setTimeout(resolve, 200));
    return mockAlertSendData;
  }

  /**
   * 根据ID数组获取告警发送列表
   */
  static async getAlertSendsByIds(ids: string[]): Promise<AlertSend[]> {
    await new Promise(resolve => setTimeout(resolve, 200));
    // 模拟根据ID查询
    return mockAlertSendData.filter(send => ids.includes(`send_${send.id}`));
  }

  /**
   * 获取其他信息列表
   */
  static async getOtherInfos(): Promise<OtherInfo[]> {
    await new Promise(resolve => setTimeout(resolve, 200));
    return mockOtherInfoData;
  }

  /**
   * 根据ID获取其他信息
   */
  static async getOtherInfoById(id: string): Promise<OtherInfo | null> {
    await new Promise(resolve => setTimeout(resolve, 200));
    // 模拟根据ID查询
    const info = mockOtherInfoData.find(info => `info_${info.id}` === id);
    return info || null;
  }

  /**
   * 保存复合表单数据
   */
  static async saveComplexForm(
    data: TaskBasicFormDataAdd
  ): Promise<TaskBasicFormDataAdd> {
    console.log('保存复合表单数据:', data);
    await new Promise(resolve => setTimeout(resolve, 800));

    // 模拟返回新创建的任务
    // const newTask: TaskBasicFormDataAdd = {
    //   id: Date.now(),
    //   ...data.task_exec,
    //   createTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
    //   status: 'enabled',
    // };

    return data;
  }

  /**
   * 更新复合表单数据
   */
  static async updateComplexForm(
    id: number,
    data: TaskBasicFormDataUpdateOrDelete
  ): Promise<TaskBasicFormDataUpdateOrDelete> {
    console.log('更新复合表单数据:', id, data);
    await new Promise(resolve => setTimeout(resolve, 800));

    const existingTask = mockTaskData.find(task => task.id === id);
    if (!existingTask) {
      throw new Error('任务不存在');
    }

    return {
      ...existingTask,
      ...data,
    };
  }
}
